"use client"

import { useEffect, useState } from "react"
import { useChat } from "ai/react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card } from "@/components/ui/card"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { MessageSquare, Send, User, Bot, Menu, LogOut, Settings, Plus, Trash2 } from "lucide-react"
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs"
import { useRouter } from "next/navigation"
import { useToast } from "@/hooks/use-toast"
import ReactMarkdown from "react-markdown"
import { motion, AnimatePresence } from "framer-motion"
import Link from "next/link"

export default function ChatPage() {
  const [conversations, setConversations] = useState<any[]>([])
  const [currentConversationId, setCurrentConversationId] = useState<string | null>(null)
  const [loadingConversations, setLoadingConversations] = useState(true)
  const [user, setUser] = useState<any>(null)
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const { messages, input, handleInputChange, handleSubmit, isLoading } = useChat()
  const supabase = createClientComponentClient()
  const router = useRouter()
  const { toast } = useToast()

  useEffect(() => {
    const getUser = async () => {
      const {
        data: { session },
      } = await supabase.auth.getSession()
      if (!session) {
        router.push("/auth/login")
        return
      }
      setUser(session.user)
    }
    getUser()
  }, [supabase, router])

  const loadConversations = async () => {
    if (!user) return

    try {
      const { data, error } = await supabase
        .from("conversations")
        .select("*")
        .eq("user_id", user.id)
        .order("updated_at", { ascending: false })

      if (error) throw error
      setConversations(data || [])
    } catch (error) {
      console.error("Error loading conversations:", error)
    } finally {
      setLoadingConversations(false)
    }
  }

  const saveConversation = async (messages: any[]) => {
    if (!user || messages.length === 0) return

    try {
      let conversationId = currentConversationId

      if (!conversationId) {
        // Create new conversation
        const title = messages[0]?.content?.slice(0, 50) + "..." || "New Chat"
        const { data: conversation, error: convError } = await supabase
          .from("conversations")
          .insert({
            user_id: user.id,
            title,
          })
          .select()
          .single()

        if (convError) throw convError
        conversationId = conversation.id
        setCurrentConversationId(conversationId)
      }

      // Save messages
      const messagesToSave = messages.map((msg) => ({
        conversation_id: conversationId,
        role: msg.role,
        content: msg.content,
      }))

      await supabase.from("messages").delete().eq("conversation_id", conversationId)
      const { error: msgError } = await supabase.from("messages").insert(messagesToSave)

      if (msgError) throw msgError

      // Update conversation timestamp
      await supabase.from("conversations").update({ updated_at: new Date().toISOString() }).eq("id", conversationId)

      loadConversations()
    } catch (error) {
      console.error("Error saving conversation:", error)
    }
  }

  const loadConversation = async (conversationId: string) => {
    try {
      const { data, error } = await supabase
        .from("messages")
        .select("*")
        .eq("conversation_id", conversationId)
        .order("created_at", { ascending: true })

      if (error) throw error

      setCurrentConversationId(conversationId)
      // You would need to set the messages in the chat hook here
      // This requires modifying the useChat hook or using a custom implementation
    } catch (error) {
      console.error("Error loading conversation:", error)
    }
  }

  const deleteConversation = async (conversationId: string) => {
    try {
      const { error } = await supabase.from("conversations").delete().eq("id", conversationId)

      if (error) throw error

      if (currentConversationId === conversationId) {
        setCurrentConversationId(null)
        newChat()
      }

      loadConversations()
      toast({
        title: "Conversation deleted",
        description: "The conversation has been removed.",
      })
    } catch (error) {
      console.error("Error deleting conversation:", error)
      toast({
        title: "Error",
        description: "Failed to delete conversation",
        variant: "destructive",
      })
    }
  }

  useEffect(() => {
    if (user) {
      loadConversations()
    }
  }, [user])

  useEffect(() => {
    if (messages.length > 0) {
      saveConversation(messages)
    }
  }, [messages])

  const handleLogout = async () => {
    await supabase.auth.signOut()
    toast({
      title: "Logged out",
      description: "You have been successfully logged out.",
    })
    router.push("/")
  }

  const newChat = () => {
    setCurrentConversationId(null)
    window.location.reload()
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <MessageSquare className="h-12 w-12 text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="flex h-screen bg-gray-50">
      {/* Sidebar */}
      <AnimatePresence>
        {sidebarOpen && (
          <motion.div
            initial={{ x: -300 }}
            animate={{ x: 0 }}
            exit={{ x: -300 }}
            transition={{ type: "spring", damping: 20 }}
            className="fixed inset-y-0 left-0 z-50 w-80 bg-white border-r border-gray-200 lg:relative lg:translate-x-0"
          >
            <div className="flex flex-col h-full">
              {/* Sidebar Header */}
              <div className="flex items-center justify-between p-4 border-b">
                <div className="flex items-center space-x-2">
                  <MessageSquare className="h-6 w-6 text-blue-600" />
                  <span className="font-semibold text-gray-900">ProjectPersona</span>
                </div>
                <Button variant="ghost" size="sm" onClick={() => setSidebarOpen(false)} className="lg:hidden">
                  ×
                </Button>
              </div>

              {/* New Chat Button */}
              <div className="p-4">
                <Button onClick={newChat} className="w-full bg-transparent" variant="outline">
                  <Plus className="h-4 w-4 mr-2" />
                  New Chat
                </Button>
              </div>

              {/* Chat History */}
              <ScrollArea className="flex-1 px-4">
                <div className="space-y-2">
                  <div className="text-xs font-medium text-gray-500 uppercase tracking-wider mb-2">Recent Chats</div>
                  {loadingConversations ? (
                    <div className="text-sm text-gray-500">Loading...</div>
                  ) : conversations.length > 0 ? (
                    conversations.map((conversation) => (
                      <Card
                        key={conversation.id}
                        className={`p-3 cursor-pointer hover:bg-gray-50 transition-colors ${
                          currentConversationId === conversation.id ? "bg-blue-50 border-blue-200" : ""
                        }`}
                        onClick={() => loadConversation(conversation.id)}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex-1 min-w-0">
                            <p className="text-sm text-gray-700 truncate">{conversation.title}</p>
                            <p className="text-xs text-gray-500 mt-1">
                              {new Date(conversation.updated_at).toLocaleDateString()}
                            </p>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation()
                              deleteConversation(conversation.id)
                            }}
                            className="opacity-0 group-hover:opacity-100 transition-opacity"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </Card>
                    ))
                  ) : (
                    <div className="text-sm text-gray-500">No conversations yet</div>
                  )}
                </div>
              </ScrollArea>

              {/* User Menu */}
              <div className="border-t p-4">
                <div className="flex items-center space-x-3 mb-3">
                  <Avatar>
                    <AvatarFallback>{user.email?.charAt(0).toUpperCase()}</AvatarFallback>
                  </Avatar>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-gray-900 truncate">{user.email}</p>
                    <p className="text-xs text-gray-500">Free Plan</p>
                  </div>
                </div>
                <div className="space-y-1">
                  <Link href="/profile">
                    <Button variant="ghost" size="sm" className="w-full justify-start">
                      <User className="h-4 w-4 mr-2" />
                      Profile
                    </Button>
                  </Link>
                  <Link href="/settings">
                    <Button variant="ghost" size="sm" className="w-full justify-start">
                      <Settings className="h-4 w-4 mr-2" />
                      Settings
                    </Button>
                  </Link>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50"
                    onClick={handleLogout}
                  >
                    <LogOut className="h-4 w-4 mr-2" />
                    Logout
                  </Button>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <div className="bg-white border-b border-gray-200 p-4 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Button variant="ghost" size="sm" onClick={() => setSidebarOpen(!sidebarOpen)} className="lg:hidden">
              <Menu className="h-5 w-5" />
            </Button>
            <Button variant="ghost" size="sm" onClick={() => setSidebarOpen(!sidebarOpen)} className="hidden lg:flex">
              <Menu className="h-5 w-5" />
            </Button>
            <h1 className="text-lg font-semibold text-gray-900">{messages.length === 0 ? "New Chat" : "Chat"}</h1>
          </div>
        </div>

        {/* Messages */}
        <ScrollArea className="flex-1 p-4">
          <div className="max-w-4xl mx-auto space-y-6">
            {messages.length === 0 ? (
              <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} className="text-center py-12">
                <MessageSquare className="h-16 w-16 text-blue-600 mx-auto mb-4" />
                <h2 className="text-2xl font-semibold text-gray-900 mb-2">Welcome to ProjectPersona</h2>
                <p className="text-gray-600 mb-6">
                  Start a conversation with your AI assistant. Ask questions, get help, or just chat!
                </p>
                <div className="grid md:grid-cols-2 gap-4 max-w-2xl mx-auto">
                  <Card className="p-4 cursor-pointer hover:shadow-md transition-shadow">
                    <h3 className="font-medium mb-2">💡 Get Ideas</h3>
                    <p className="text-sm text-gray-600">Brainstorm creative solutions and innovative concepts</p>
                  </Card>
                  <Card className="p-4 cursor-pointer hover:shadow-md transition-shadow">
                    <h3 className="font-medium mb-2">📝 Write Content</h3>
                    <p className="text-sm text-gray-600">Create articles, emails, and other written content</p>
                  </Card>
                  <Card className="p-4 cursor-pointer hover:shadow-md transition-shadow">
                    <h3 className="font-medium mb-2">🔍 Learn & Research</h3>
                    <p className="text-sm text-gray-600">Get explanations and research assistance on any topic</p>
                  </Card>
                  <Card className="p-4 cursor-pointer hover:shadow-md transition-shadow">
                    <h3 className="font-medium mb-2">🛠️ Problem Solving</h3>
                    <p className="text-sm text-gray-600">Work through challenges and find practical solutions</p>
                  </Card>
                </div>
              </motion.div>
            ) : (
              <AnimatePresence>
                {messages.map((message, index) => (
                  <motion.div
                    key={message.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className={`flex items-start space-x-3 ${
                      message.role === "user" ? "justify-end" : "justify-start"
                    }`}
                  >
                    {message.role === "assistant" && (
                      <Avatar className="mt-1">
                        <AvatarFallback className="bg-blue-600 text-white">
                          <Bot className="h-4 w-4" />
                        </AvatarFallback>
                      </Avatar>
                    )}
                    <div
                      className={`max-w-3xl rounded-lg p-4 ${
                        message.role === "user" ? "bg-blue-600 text-white ml-12" : "bg-white border border-gray-200"
                      }`}
                    >
                      {message.role === "user" ? (
                        <p className="whitespace-pre-wrap">{message.content}</p>
                      ) : (
                        <div className="prose prose-sm max-w-none">
                          <ReactMarkdown>{message.content}</ReactMarkdown>
                        </div>
                      )}
                    </div>
                    {message.role === "user" && (
                      <Avatar className="mt-1">
                        <AvatarFallback>
                          <User className="h-4 w-4" />
                        </AvatarFallback>
                      </Avatar>
                    )}
                  </motion.div>
                ))}
              </AnimatePresence>
            )}

            {isLoading && (
              <motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} className="flex items-start space-x-3">
                <Avatar>
                  <AvatarFallback className="bg-blue-600 text-white">
                    <Bot className="h-4 w-4" />
                  </AvatarFallback>
                </Avatar>
                <div className="bg-white border border-gray-200 rounded-lg p-4">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                    <div
                      className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                      style={{ animationDelay: "0.1s" }}
                    ></div>
                    <div
                      className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                      style={{ animationDelay: "0.2s" }}
                    ></div>
                  </div>
                </div>
              </motion.div>
            )}
          </div>
        </ScrollArea>

        {/* Input */}
        <div className="border-t bg-white p-4">
          <div className="max-w-4xl mx-auto">
            <form onSubmit={handleSubmit} className="flex space-x-3">
              <Input
                value={input}
                onChange={handleInputChange}
                placeholder="Type your message..."
                className="flex-1"
                disabled={isLoading}
              />
              <Button type="submit" disabled={isLoading || !input.trim()}>
                <Send className="h-4 w-4" />
              </Button>
            </form>
            <p className="text-xs text-gray-500 text-center mt-2">
              ProjectPersona can make mistakes. Consider checking important information.
            </p>
          </div>
        </div>
      </div>

      {/* Overlay for mobile sidebar */}
      {sidebarOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden" onClick={() => setSidebarOpen(false)} />
      )}
    </div>
  )
}
